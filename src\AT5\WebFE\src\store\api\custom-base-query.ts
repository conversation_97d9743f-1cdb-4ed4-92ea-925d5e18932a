import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import type { AppConfig } from '@/config/app';

/**
 * Custom base query that handles 401 responses by redirecting to a configured login page
 * with a return URL parameter. Based on RTK Query official documentation pattern.
 */
export const createCustomBaseQuery = (config: AppConfig): BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> => {
  const apiConfig = config.api;
  
  const baseQuery = fetchBaseQuery({
    baseUrl: apiConfig.baseUrl,
    prepareHeaders: (headers) => {
      if (apiConfig.tenantId) {
        headers.set('tenant-id', apiConfig.tenantId.toString());
      }
      if (apiConfig.userId) {
        headers.set('user-id', apiConfig.userId.toString());
      }
      if (apiConfig.xApiKey) {
        headers.set('X-API-Key', apiConfig.xApiKey);
      }
      return headers;
    },
  });

  return async (args, api, extraOptions) => {
    const result = await baseQuery(args, api, extraOptions);
    const redirectUri = config.notAuthorizedRedirectPath;

    if (result.error?.status === 401 && redirectUri) {
      try {
        const currentUrl = window.location.href;
        const redirectUrl = new URL(redirectUri, window.location.origin);

        // Prevent redirect loop by only adding returnUrl if not already on the redirect page.
        if (!currentUrl.includes(redirectUri)) {
          redirectUrl.searchParams.set('returnUrl', encodeURIComponent(currentUrl));
        }

        window.location.href = redirectUrl.toString();
      } catch (error) {
        console.error('Failed to construct redirect URL with returnUrl:', error);
        window.location.href = redirectUri;
      }
    }

    return result;
  };
};
