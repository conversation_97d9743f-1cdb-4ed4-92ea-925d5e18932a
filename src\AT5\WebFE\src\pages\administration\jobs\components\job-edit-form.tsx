'use client';

import * as React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Divider from '@mui/material/Divider';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import InputLabel from '@mui/material/InputLabel';
import Link from '@mui/material/Link';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import Switch from '@mui/material/Switch';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { CaretDownIcon } from '@phosphor-icons/react/dist/ssr/CaretDown';
import { Controller, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { z as zod } from 'zod';

import { RouterLink } from '@/components/core/link';
import { Option } from '@/components/core/option';
import { toast } from '@/components/core/toaster';
import { logger } from '@/lib/default-logger';
import { paths } from '@/paths';

import { useCreateJobMutation, useGetJobQuery, useUpdateJobMutation } from '@/store/api/jobs-api';
import { JobsEmailsList } from './jobs-emails-list';

const JobTypeOptions = [
	{ label: 'JobType.AgencyWorkersDeactivationJob', value: 0 },
	{ label: 'JobType.NullJob', value: 1 },
	{ label: 'JobType.ReportJob', value: 2 },
] as const;

const schema = zod.object({
	name: zod.string().min(1, 'Name is required').max(255),
	type: zod.number().gte(0).lte(JobTypeOptions.length),
	description: zod.string().max(255).optional(),
	parameters: zod.string().min(1, 'Parameters are required'),
	successEmails: zod.string().max(512).optional(), // .email('Must be a valid email')
	errorEmails: zod.string().max(512).optional(),
	disabled: zod.boolean()
});

type Values = zod.infer<typeof schema>;

const defaultValues = {
	name: '',
    type: 0,
    description:'',
    parameters:'',
	successEmails: '',
	errorEmails: '',
    disabled: false,	
} satisfies Values;

interface JobEditFormProps {
	jobId?: number;
}

export function JobEditForm({ jobId }: JobEditFormProps): React.JSX.Element {
	const navigate = useNavigate();
	const isEditMode = jobId !== undefined;

//	RTK Query hooks
	const { data: jobData, error: jobError, isLoading: isLoadingJob } = useGetJobQuery(jobId!, {
		skip: !isEditMode || !jobId,
	});
	const [createJob, { isLoading: isCreating }] = useCreateJobMutation();
	const [updateJob, { isLoading: isUpdating }] = useUpdateJobMutation();

	const {
		control,
		handleSubmit,
		formState: { errors },
		setValue,
		watch,
		reset,
	} = useForm<Values>({ defaultValues, resolver: zodResolver(schema) });

	// Handle job data loading for edit mode
	React.useEffect(() => {
		if (isEditMode && jobData?.entity) {
			const formData: Values = {
				...jobData.entity
			};
			reset(formData);
		}
	}, [jobData, isEditMode, reset]);

	// Handle job loading error
	React.useEffect(() => {
		if (jobError) {
			logger.error(jobError);
			toast.error('Failed to load job data');
		}
	}, [jobError]);

	const handleEmailsChange = React.useCallback((successEmails: string[], failureEmails: string[]) => {
		setValue('successEmails', successEmails.join(';'));
		setValue('errorEmails', failureEmails.join(';'));
	}, [setValue]);

	const successEmailsString = watch('successEmails') ?? '';
	const errorEmailsString = watch('errorEmails') ?? '';

	const onSubmit = React.useCallback(
		async (formValues: Values): Promise<void> => {
			try {
				if (isEditMode && jobId) {
					await updateJob({
						id: jobId,
						job: formValues
					}).unwrap();
					toast.success('Job updated');
					navigate(paths.administration.jobs.edit(jobId));
				} else {
					// Map form values to create request format
					const createRequest = {
						name: formValues.name,
						type: formValues.type,
						description: formValues.description,
						parameters: formValues.parameters,
						emailSuccess: formValues.successEmails,
						emailFail: formValues.errorEmails,
						disabled: formValues.disabled,
					};
					const {id} = await createJob(createRequest).unwrap();
					toast.success('Job created');
					navigate(paths.administration.jobs.edit(id, true));
				}
			} catch (error) {
				logger.error(error);
				toast.error('Something went wrong!');
			}
		},
		[navigate, isEditMode, jobId, updateJob, createJob]
	);

	return (
		<form onSubmit={handleSubmit(onSubmit)}>
			<Card>
				<CardActions sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
					<Typography variant="h4">{isEditMode ? 'Edit job' : 'Create new job'}</Typography>
					<Box>
						<Button color="secondary" component={RouterLink} href={paths.administration.jobs.index()}>
							Cancel
						</Button>
						<Button type="submit" variant="contained">
							{isEditMode ? 'Update' : 'Create'}
						</Button>
					</Box>
				</CardActions>
				<CardContent>
					<Stack divider={<Divider />} spacing={4}>
						<Stack spacing={3}>
							<Typography variant="h5">Detail</Typography>
							<Grid container spacing={3} 					
                                direction="row"
                                sx={{
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}
                            >
								<Grid
									size={{
										md: 6,
										xs: 12,
									}}
								>
									<Controller
										control={control}
										name="name"
										render={({ field }) => (
                                            <Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
                                                <FormControl error={Boolean(errors.name)} fullWidth>
                                                    <InputLabel required>Job Name</InputLabel>
                                                    <OutlinedInput required {...field} />
                                                    {errors.name ? <FormHelperText>{errors.name.message}</FormHelperText> : null}
                                                </FormControl>
                                            </Tooltip>
										)}
									/>
								</Grid>
                                <Grid
									size={{
										md: 6,
										xs: 12,
									}}
								>
									<Controller
										control={control}
										name="type"
										render={({ field }) => (
											<Autocomplete
												{...field}
												getOptionLabel={(option) => option.label}
												onChange={(_, value) => {
													if (value) {
														field.onChange(value.value);
													}
												}}
												options={JobTypeOptions}
												popupIcon={<CaretDownIcon fontSize="var(--icon-fontSize-sm)" />}
												renderInput={(params) => (
													<FormControl error={Boolean(errors.type)} fullWidth>
														<InputLabel>Job Type</InputLabel>
														<OutlinedInput
															{...params.InputProps}
															inputProps={params.inputProps}
														/>
														{errors.type ? (
															<FormHelperText>{errors.type.message}</FormHelperText>
														) : null}
													</FormControl>
												)}
												renderOption={(props, option) => (
													<Option {...props} key={option.value} value={option.value}>
														{option.label}
													</Option>
												)}
												value={JobTypeOptions.find((option) => option.value === field.value)}
											/>
										)}
									/>
								</Grid>
                                <Grid
									size={{
										xs: 12,
									}}
								>
									<Controller
										control={control}
										name="description"
										render={({ field }) => (
                                            <Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
                                                <FormControl error={Boolean(errors.description)} fullWidth>
                                                    <InputLabel>Description</InputLabel>
                                                    <OutlinedInput multiline {...field} />
                                                    {errors.description ? <FormHelperText>{errors.description.message}</FormHelperText> : null}
                                                </FormControl>
                                            </Tooltip>
										)}
									/>
								</Grid>
                                <Grid
									size={{
										xs: 12,
									}}
								>
									<Controller
										control={control}
										name="parameters"
										render={({ field }) => (
                                            <Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
                                                <FormControl error={Boolean(errors.name)} fullWidth>
                                                    <InputLabel required>Parameters</InputLabel>
                                                    <OutlinedInput required multiline {...field} />
                                                    {errors.parameters ? <FormHelperText>{errors.parameters.message}</FormHelperText> : null}
                                                </FormControl>
                                            </Tooltip>
										)}
									/>
								</Grid>
								<Grid
									size={{
										xs: 12,
									}}>
									<Divider />
										<Stack direction={'column'}>
											<Typography> Emails </Typography>
											<JobsEmailsList
												errorEmails={errorEmailsString.split(';').map((e: string) => e.trim()).filter((e: string) => e)}
												successEmails={successEmailsString.split(';').map((e: string) => e.trim()).filter((e: string) => e)}
												onEmailsChange={handleEmailsChange}
											/>
										</Stack>
									<Divider />
								</Grid>
								<Controller
									control={control}
									name="successEmails"
									render={({ field }) => (
										<input {...field} type="hidden" />
									)}
								/>
								<Controller
									control={control}
									name="errorEmails"
									render={({ field }) => (
										<input {...field} type="hidden" />
									)}
								/>
							</Grid>
						</Stack>
					</Stack>
				</CardContent>
				<CardActions sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
					<Controller
						control={control}
						name="disabled"
						render={({ field }) => (
							<FormControlLabel
								control={<Switch checked={field.value} onChange={field.onChange} />}
								label="Disabled"
							/>
						)}
					/>
					<Box>
						<Button color="secondary" component={RouterLink} href={paths.administration.jobs.index()}>
							Cancel
						</Button>
						<Button type="submit" variant="contained">
							{isEditMode ? 'Update' : 'Create'}
						</Button>
					</Box>
				</CardActions>
			</Card>
		</form>
	);
}
